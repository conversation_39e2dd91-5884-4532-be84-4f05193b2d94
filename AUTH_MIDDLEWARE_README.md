# Authentication Middleware Implementation

This document describes the implementation of authentication middleware for the RenoRetail FastAPI application.

## Overview

All authentication logic has been moved from individual route handlers to a centralized middleware system. This provides:

- **Centralized Authentication**: All JWT validation logic is in one place
- **Automatic Token Validation**: Middleware automatically validates tokens for protected routes
- **Request State Management**: User information is automatically added to request state
- **Flexible Path Exclusion**: Easy configuration of which paths require authentication
- **Better Error Handling**: Consistent authentication error responses

## Architecture Changes

### Before (api.py)
```python
# Authentication logic was scattered in api.py
from fastapi.security import HTTPBearer
from jose import jwt
import boto3

security = HTTPBearer()
JWKS = boto3.client("http").get(JWKS_URL).json()  # This was broken

def verify_jwt(token: str):
    # JWT verification logic
    pass

def get_current_user(credentials = Depends(security)):
    # User extraction logic
    pass

@app.get("/items/{item_id}")
def read_item(item_id: str, user=Depends(get_current_user)):
    # Route handler with explicit dependency
    pass
```

### After (middleware.py + api.py)
```python
# middleware.py - Centralized authentication
class AuthenticationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Automatic token validation and user state management
        pass

# api.py - Clean route handlers
@app.get("/items/{item_id}")
async def read_item(item_id: str, request: Request):
    user = get_current_user(request)  # Get user from request state
    # Route logic
```

## Key Components

### 1. AuthenticationMiddleware Class

Located in `middleware.py`, this class:
- Inherits from `BaseHTTPMiddleware`
- Automatically intercepts all requests
- Validates JWT tokens against AWS Cognito JWKS
- Adds user information to `request.state`
- Handles authentication errors consistently

### 2. Helper Functions

- `get_current_user(request)`: Extracts authenticated user from request state
- `require_auth(request)`: Ensures request is authenticated (can be used as dependency)

### 3. Configuration

The middleware is configured in `api.py`:
```python
app.add_middleware(
    AuthenticationMiddleware,
    cognito_region=COGNITO_REGION,
    user_pool_id=USER_POOL_ID,
    app_client_id=APP_CLIENT_ID,
    excluded_paths=["/docs", "/openapi.json", "/redoc", "/health", "/"]
)
```

## Usage Examples

### Protected Route Handler
```python
@app.get("/items/{item_id}")
async def read_item(item_id: str, request: Request):
    # User is automatically authenticated by middleware
    user = get_current_user(request)
    
    # Access user information
    user_id = user.get("sub")
    email = user.get("email")
    
    # Your business logic here
    return {"item_id": item_id, "user_id": user_id}
```

### Public Route (No Authentication)
```python
@app.get("/health")
async def health_check():
    # This endpoint is in excluded_paths, so no authentication required
    return {"status": "healthy"}
```

### Using as Dependency
```python
from fastapi import Depends
from middleware import require_auth

@app.get("/protected")
async def protected_endpoint(request: Request, _: None = Depends(require_auth)):
    user = get_current_user(request)
    return {"message": "You are authenticated", "user": user}
```

## Configuration Options

### Environment Variables
```bash
COGNITO_REGION=us-east-1
USER_POOL_ID=us-east-1_XXXXXXX
APP_CLIENT_ID=your_client_id
```

### Middleware Parameters
- `cognito_region`: AWS region for Cognito
- `user_pool_id`: Cognito User Pool ID
- `app_client_id`: Cognito App Client ID
- `excluded_paths`: List of paths that don't require authentication

## Error Responses

### Missing Token
```json
{
  "detail": "Authorization header missing or invalid"
}
```

### Invalid Token
```json
{
  "detail": "Invalid token: Key not found"
}
```

### Expired Token
```json
{
  "detail": "Invalid token: Signature has expired"
}
```

## Testing

Run the test suite:
```bash
# Install test dependencies
pip install pytest

# Run tests
python test_auth_middleware.py
# or
pytest test_auth_middleware.py -v
```

## Dependencies Added

- `requests==2.31.0` - For fetching JWKS from Cognito

## Migration Notes

### For Existing Routes
1. Remove `user=Depends(get_current_user)` from route parameters
2. Add `request: Request` parameter
3. Use `get_current_user(request)` inside the function to get user data

### Before:
```python
@app.get("/items/{item_id}")
def read_item(item_id: str, user=Depends(get_current_user)):
    return {"item": item_id, "user": user.get("sub")}
```

### After:
```python
@app.get("/items/{item_id}")
async def read_item(item_id: str, request: Request):
    user = get_current_user(request)
    return {"item": item_id, "user": user.get("sub")}
```

## Benefits

1. **Cleaner Code**: Route handlers focus on business logic
2. **Centralized Security**: All auth logic in one place
3. **Better Performance**: No dependency injection overhead
4. **Easier Testing**: Mock middleware instead of individual dependencies
5. **Consistent Errors**: Uniform error responses across all endpoints
6. **Flexible Configuration**: Easy to add/remove protected paths

## Security Considerations

1. **JWKS Caching**: Consider implementing JWKS caching for production
2. **Token Refresh**: Implement token refresh logic if needed
3. **Rate Limiting**: Add rate limiting middleware for additional security
4. **Logging**: Monitor authentication attempts and failures
5. **HTTPS Only**: Ensure all traffic uses HTTPS in production
