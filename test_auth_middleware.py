"""
Test script for authentication middleware functionality.

This script demonstrates how the authentication middleware works and provides
basic testing functionality.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json

from api import app


class TestAuthenticationMiddleware:
    """Test cases for the authentication middleware."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_health_endpoint_no_auth_required(self):
        """Test that health endpoint doesn't require authentication."""
        response = self.client.get("/")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_protected_endpoint_without_token(self):
        """Test that protected endpoints require authentication."""
        response = self.client.get("/items/123")
        assert response.status_code == 401
        assert "Authorization header missing" in response.json()["detail"]
    
    def test_protected_endpoint_with_invalid_token(self):
        """Test protected endpoint with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = self.client.get("/items/123", headers=headers)
        assert response.status_code == 401
    
    @patch('middleware.AuthenticationMiddleware._verify_jwt')
    def test_protected_endpoint_with_valid_token(self, mock_verify):
        """Test protected endpoint with valid token."""
        # Mock JWT verification to return user data
        mock_verify.return_value = {
            "sub": "user123",
            "email": "<EMAIL>",
            "cognito:username": "testuser"
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        
        # Mock DynamoDB response
        with patch('api.get_item_dynamodb') as mock_db:
            mock_db.return_value = {"id": "123", "name": "Test Item"}
            
            response = self.client.get("/items/123", headers=headers)
            assert response.status_code == 200
            
            data = response.json()
            assert data["item"]["id"] == "123"
            assert data["requested_by"] == "user123"
            assert data["user_email"] == "<EMAIL>"
    
    @patch('middleware.AuthenticationMiddleware._verify_jwt')
    def test_profile_endpoint(self, mock_verify):
        """Test profile endpoint with valid token."""
        mock_verify.return_value = {
            "sub": "user123",
            "email": "<EMAIL>",
            "cognito:username": "testuser",
            "token_use": "access",
            "auth_time": 1234567890,
            "iss": "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_XXXXXXX",
            "exp": 1234567890
        }
        
        headers = {"Authorization": "Bearer valid_token"}
        response = self.client.get("/profile", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["user_id"] == "user123"
        assert data["email"] == "<EMAIL>"
        assert data["username"] == "testuser"


def test_middleware_configuration():
    """Test that middleware is properly configured."""
    # Check that middleware is added to the app
    middleware_classes = [middleware.cls for middleware in app.user_middleware]
    
    from middleware import AuthenticationMiddleware
    assert AuthenticationMiddleware in middleware_classes


if __name__ == "__main__":
    # Run basic tests
    print("Testing authentication middleware...")
    
    client = TestClient(app)
    
    # Test health endpoint
    print("1. Testing health endpoint (no auth required)...")
    response = client.get("/")
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.json()}")
    
    # Test protected endpoint without token
    print("\n2. Testing protected endpoint without token...")
    response = client.get("/items/123")
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.json()}")
    
    # Test protected endpoint with invalid token
    print("\n3. Testing protected endpoint with invalid token...")
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/items/123", headers=headers)
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.json()}")
    
    print("\nBasic tests completed!")
    print("\nTo run full tests with pytest:")
    print("pip install pytest")
    print("pytest test_auth_middleware.py -v")
