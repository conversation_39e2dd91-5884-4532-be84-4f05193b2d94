from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt
import boto3
import os

app = FastAPI()
security = HTTPBearer()

COGNITO_REGION = "us-east-1"
USER_POOL_ID = "us-east-1_XXXXXXX"
APP_CLIENT_ID = "your_client_id"
COGNITO_ISSUER = f"https://cognito-idp.{COGNITO_REGION}.amazonaws.com/{USER_POOL_ID}"

# JWKS for Cognito
JWKS_URL = f"{COGNITO_ISSUER}/.well-known/jwks.json"
JWKS = boto3.client("http").get(JWKS_URL).json()

def verify_jwt(token: str):
    headers = jwt.get_unverified_header(token)
    key = next((k for k in JWKS["keys"] if k["kid"] == headers["kid"]), None)
    if not key:
        raise HTTPException(status_code=401, detail="Invalid token")

    return jwt.decode(token, key, algorithms=["RS256"], issuer=COGNITO_ISSUER)

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = verify_jwt(credentials.credentials)
        return payload
    except Exception as e:
        raise HTTPException(status_code=401, detail=str(e))

@app.get("/items/{item_id}")
def read_item(item_id: str, user=Depends(get_current_user)):
    return get_item_dynamodb(item_id)