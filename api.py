"""
RenoRetail API - FastAPI application with JWT authentication middleware.

This module provides a REST API for the RenoRetail application with automatic
JWT authentication using AWS Cognito through custom middleware.
"""
import os
from typing import Dict, Any

import boto3
from fastapi import FastAPI, Request

from middleware import AuthenticationMiddleware, get_current_user

# Initialize FastAPI app
app = FastAPI(
    title="RenoRetail API",
    version="1.0.0",
    docs_url="/docs",
    openapi_url="/openapi.json",
    redoc_url="/redoc"
)

# Add authentication middleware
# Cognito configuration is now handled in middleware.py via environment variables
app.add_middleware(
    AuthenticationMiddleware,
    excluded_paths=["/docs", "/openapi.json", "/redoc", "/health", "/"]
)

# DynamoDB setup
dynamodb = boto3.resource('dynamodb')
table_name = os.getenv('USERS_TABLE', 'users-table-dev')

def get_item_dynamodb(item_id: str) -> Dict[str, Any]:
    """
    Retrieve item from DynamoDB.

    Args:
        item_id: The ID of the item to retrieve

    Returns:
        Dictionary containing item data
    """
    try:
        table = dynamodb.Table(table_name)
        response = table.get_item(Key={'id': item_id})

        if 'Item' in response:
            return response['Item']
        return {"error": "Item not found"}
    except Exception as e:
        return {"error": f"Database error: {str(e)}"}

@app.get("/")
async def root():
    """Health check endpoint - no authentication required."""
    return {"message": "RenoRetail API is running", "status": "healthy"}

@app.get("/items/{item_id}")
async def read_item(item_id: str, request: Request):
    """
    Get item by ID - requires authentication.

    The authentication middleware automatically validates the JWT token
    and adds user information to request.state.user
    """
    # Get current user from request state (set by middleware)
    user = get_current_user(request)

    # Get item from database
    item = get_item_dynamodb(item_id)

    # Return item with user context
    return {
        "item": item,
        "requested_by": user.get("sub"),  # User ID from JWT
        "user_email": user.get("email"),  # Email from JWT (if available)
        "item_id": item_id
    }

@app.get("/profile")
async def get_profile(request: Request):
    """
    Get current user profile - requires authentication.
    """
    user = get_current_user(request)

    return {
        "user_id": user.get("sub"),
        "email": user.get("email"),
        "username": user.get("cognito:username"),
        "token_use": user.get("token_use"),
        "auth_time": user.get("auth_time"),
        "iss": user.get("iss"),
        "exp": user.get("exp")
    }