"""
Authentication middleware for FastAPI applications using AWS Cognito JWT tokens.

This module provides middleware that automatically validates JWT tokens from AWS Cognito
and adds user information to request state for use in route handlers.
"""
import logging
from typing import Optional, Dict, Any

from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from jose import jwt, JWTError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware for handling JWT authentication with AWS Cognito.

    This middleware intercepts all requests, validates JWT tokens, and adds
    user information to the request state for use in route handlers.
    """

    def __init__(self, app, cognito_region: str = "us-east-1",
                 user_pool_id: str = "us-east-1_XXXXXXX",
                 app_client_id: str = "your_client_id",
                 excluded_paths: Optional[list] = None):
        super().__init__(app)
        self.cognito_region = cognito_region
        self.user_pool_id = user_pool_id
        self.app_client_id = app_client_id
        self.cognito_issuer = f"https://cognito-idp.{cognito_region}.amazonaws.com/{user_pool_id}"
        self.jwks_url = f"{self.cognito_issuer}/.well-known/jwks.json"
        self.excluded_paths = excluded_paths or ["/docs", "/openapi.json", "/redoc", "/health"]
        self.security = HTTPBearer()

        # Initialize JWKS
        self._jwks = None
        self._load_jwks()

    def _load_jwks(self) -> None:
        """Load JWKS (JSON Web Key Set) from Cognito."""
        try:
            # Note: In production, you might want to cache this and refresh periodically
            import requests
            response = requests.get(self.jwks_url)
            response.raise_for_status()
            self._jwks = response.json()
            logger.info("Successfully loaded JWKS from Cognito")
        except Exception as e:
            logger.error(f"Failed to load JWKS: {str(e)}")
            # Fallback to boto3 approach (though this might not work as shown in original code)
            try:
                # The original code had an issue - boto3.client("http") doesn't exist
                # This is a placeholder for proper JWKS loading
                self._jwks = {"keys": []}
            except Exception as fallback_error:
                logger.error(f"Fallback JWKS loading also failed: {str(fallback_error)}")
                self._jwks = {"keys": []}

    def _verify_jwt(self, token: str) -> Dict[str, Any]:
        """
        Verify JWT token against Cognito JWKS.

        Args:
            token: JWT token string

        Returns:
            Decoded JWT payload

        Raises:
            HTTPException: If token is invalid
        """
        try:
            # Get token headers
            headers = jwt.get_unverified_header(token)

            # Find the key
            key = None
            for k in self._jwks.get("keys", []):
                if k.get("kid") == headers.get("kid"):
                    key = k
                    break

            if not key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: Key not found"
                )

            # Decode and verify token
            payload = jwt.decode(
                token,
                key,
                algorithms=["RS256"],
                issuer=self.cognito_issuer,
                audience=self.app_client_id
            )

            return payload

        except JWTError as e:
            logger.warning(f"JWT verification failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Invalid token: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during JWT verification: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token verification failed"
            )

    def _extract_token_from_request(self, request: Request) -> Optional[str]:
        """
        Extract JWT token from request Authorization header.

        Args:
            request: FastAPI request object

        Returns:
            JWT token string or None if not found
        """
        authorization = request.headers.get("Authorization")
        if not authorization:
            return None

        try:
            scheme, token = authorization.split(" ", 1)
            if scheme.lower() != "bearer":
                return None
            return token
        except ValueError:
            return None

    def _is_excluded_path(self, path: str) -> bool:
        """
        Check if the request path should be excluded from authentication.

        Args:
            path: Request path

        Returns:
            True if path should be excluded from authentication
        """
        return any(excluded in path for excluded in self.excluded_paths)

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Main middleware dispatch method.

        Args:
            request: FastAPI request object
            call_next: Next middleware/handler in chain

        Returns:
            Response object
        """
        # Skip authentication for excluded paths
        if self._is_excluded_path(request.url.path):
            return await call_next(request)

        # Extract token from request
        token = self._extract_token_from_request(request)

        if not token:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authorization header missing or invalid"}
            )

        try:
            # Verify token and get user payload
            user_payload = self._verify_jwt(token)

            # Add user information to request state
            request.state.user = user_payload
            request.state.authenticated = True

            # Log successful authentication (optional, remove in production)
            logger.info(f"User authenticated: {user_payload.get('sub', 'unknown')}")

        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
        except Exception as e:
            logger.error(f"Unexpected authentication error: {str(e)}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Authentication service error"}
            )

        # Continue to next middleware/handler
        response = await call_next(request)
        return response


def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Helper function to get current authenticated user from request state.

    This function can be used in route handlers to access user information
    that was set by the authentication middleware.

    Args:
        request: FastAPI request object

    Returns:
        User payload dictionary

    Raises:
        HTTPException: If user is not authenticated
    """
    if not hasattr(request.state, 'authenticated') or not request.state.authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not authenticated"
        )

    return request.state.user


def require_auth(request: Request) -> None:
    """
    Helper function to ensure request is authenticated.

    This can be used as a dependency in route handlers that require authentication.

    Args:
        request: FastAPI request object

    Raises:
        HTTPException: If user is not authenticated
    """
    if not hasattr(request.state, 'authenticated') or not request.state.authenticated:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )