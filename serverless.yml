# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: tecneti1809
# "app" enables Serverless Framework Dashboard features and sharing them with other Services.
app: testapp
# "service" is the name of this project. This will also be added to your AWS resource names.
service: renoretail

stages:
  default:
    params:
      tableName: "users-table-${sls:stage}"

plugins:
  - serverless-python-requirements
  - serverless-wsgi

custom:
  pythonRequirements:
    dockerizePip: true
  wsgi:
    app: api.app
    packRequirements: false

provider:
  name: aws
  runtime: python3.12
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - Fn::GetAtt: [UsersTable, Arn]
  environment:
    USERS_TABLE: ${param:tableName}

functions:
  api:
    handler: wsgi_handler.handler
    events:
      - http:
          path: /
          method: ANY
      - http:
          path: /{proxy+}
          method: ANY

resources:
  Resources:
    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: userId
            KeyType: HASH
        ProvisionedThroughput:
          ReadCapacityUnits: 1
          WriteCapacityUnits: 1
        TableName: ${param:tableName}